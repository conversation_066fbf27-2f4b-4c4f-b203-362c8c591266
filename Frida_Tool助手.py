import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import subprocess

class FridaTool:
    def __init__(self, root):
        self.root = root
        self.root.title("Frida Tool助手")
        self.root.geometry("500x400")

        # 现代化主题
        style = ttk.Style()
        style.theme_use('clam')

        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # IP和端口输入
        ttk.Label(main_frame, text="ADB连接设置:", font=('Arial', 12, 'bold')).grid(row=0, column=0, columnspan=2, pady=10)

        ttk.Label(main_frame, text="IP地址:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.ip_entry = ttk.Entry(main_frame, width=30)
        self.ip_entry.grid(row=1, column=1, pady=5)

        ttk.Label(main_frame, text="端口:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.port_entry = ttk.Entry(main_frame, width=30)
        self.port_entry.grid(row=2, column=1, pady=5)

        # 连接按钮
        self.connect_button = ttk.Button(main_frame, text="连接ADB", command=self.connect_adb, style='TButton')
        self.connect_button.grid(row=3, column=0, columnspan=2, pady=10)

        # Frida服务器管理
        ttk.Label(main_frame, text="Frida服务器管理:", font=('Arial', 12, 'bold')).grid(row=4, column=0, columnspan=2, pady=10)

        self.upload_button = ttk.Button(main_frame, text="上传frida-server", command=self.upload_frida, style='TButton')
        self.upload_button.grid(row=5, column=0, columnspan=2, pady=5)

        self.start_button = ttk.Button(main_frame, text="启动frida-server", command=self.start_frida, style='TButton')
        self.start_button.grid(row=6, column=0, columnspan=2, pady=5)

        # 状态栏
        self.status_var = tk.StringVar()
        self.status_bar = ttk.Label(main_frame, textvariable=self.status_var, anchor=tk.W, relief=tk.SUNKEN)
        self.status_bar.grid(row=7, column=0, columnspan=2, sticky=tk.EW, pady=10)

        # 设置按钮样式
        style.configure('TButton', foreground='white', background='#4CAF50', font=('Arial', 10, 'bold'))

        # 初始化默认值
        self.ip_entry.insert(0, "***********")
        self.port_entry.insert(0, "5555")

    def update_status(self, message):
        self.status_var.set(message)

    def connect_adb(self):
        ip = self.ip_entry.get()
        port = self.port_entry.get()

        if not ip or not port:
            messagebox.showwarning("警告", "请输入IP地址和端口")
            return

        try:
            subprocess.run(["adb", "disconnect"], check=True)
            subprocess.run(["adb", "connect", f"{ip}:{port}"], check=True)
            self.update_status("ADB连接成功")
            messagebox.showinfo("成功", "ADB连接成功")
        except subprocess.CalledProcessError as e:
            self.update_status("ADB连接失败")
            messagebox.showerror("错误", f"ADB连接失败: {e}")

    def upload_frida(self):
        try:
            # 选择frida-server文件
            file_path = filedialog.askopenfilename(title="选择frida-server文件", filetypes=[("可执行文件", "*.so"), ("所有文件", "*.*")])
            if not file_path:
                return

            # 上传到/data/local/frida-server
            subprocess.run(["adb", "push", file_path, "/data/local/tmp/frida-server"], check=True)
            subprocess.run(["adb", "shell", "mv", "/data/local/tmp/frida-server", "/data/local/frida-server"], check=True)
            subprocess.run(["adb", "shell", "chmod", "777", "/data/local/frida-server"], check=True)

            self.update_status("frida-server上传成功")
            messagebox.showinfo("成功", "frida-server上传成功")
        except subprocess.CalledProcessError as e:
            self.update_status("上传失败")
            messagebox.showerror("错误", f"上传失败: {e}")

    def start_frida(self):
        try:
            # 检查frida-server版本
            result = subprocess.run(["adb", "shell", "ls", "/data/local/frida-server*"], capture_output=True, text=True)
            servers = result.stdout.splitlines()

            if len(servers) == 0:
                messagebox.showwarning("警告", "未找到frida-server")
                return

            if len(servers) > 1:
                messagebox.showwarning("警告", "存在多个frida-server版本，请手动选择")
                return

            # 启动frida-server
            subprocess.run(["adb", "shell", "su", "-c", "chmod 777 /data/local/frida-server"], check=True)
            subprocess.run(["adb", "shell", "su", "-c", "/data/local/frida-server &"], check=True)

            self.update_status("frida-server启动成功")
            messagebox.showinfo("成功", "frida-server启动成功")
        except subprocess.CalledProcessError as e:
            self.update_status("启动失败")
            messagebox.showerror("错误", f"启动失败: {e}")

if __name__ == "__main__":
    root = tk.Tk()
    app = FridaTool(root)
    root.mainloop()
